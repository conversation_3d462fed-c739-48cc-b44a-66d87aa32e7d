import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:file_selector/file_selector.dart';
import '../../controllers/smart_composer_controller.dart';
import '../../controllers/ai_file_editor_controller.dart';
import '../../models/smart_composer_models.dart';
import '../../models/novel.dart';
import '../../widgets/file_edit_preview_widget.dart';
import 'smart_composer_settings_screen.dart';

/// Smart Composer 聊天界面
class SmartComposerChatScreen extends StatefulWidget {
  const SmartComposerChatScreen({super.key});

  @override
  State<SmartComposerChatScreen> createState() => _SmartComposerChatScreenState();
}

class _SmartComposerChatScreenState extends State<SmartComposerChatScreen> {
  final SmartComposerController _controller = Get.find<SmartComposerController>();
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  
  ChatSession? _session;
  Novel? _novel;
  Chapter? _currentChapter;

  @override
  void initState() {
    super.initState();
    _initializeFromArguments();
  }

  void _initializeFromArguments() {
    final args = Get.arguments as Map<String, dynamic>?;
    if (args != null) {
      _session = args['session'] as ChatSession?;
      _novel = args['novel'] as Novel?;
      _currentChapter = args['chapter'] as Chapter?;
      
      if (_session != null) {
        _controller.switchToSession(_session!);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Obx(() {
          final session = _controller.currentSession.value;
          return Text(session?.title ?? 'AI 写作助手');
        }),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _showSettingsDialog,
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'new_chat':
                  _startNewChat();
                  break;
                case 'clear_history':
                  _clearChatHistory();
                  break;
                case 'export_chat':
                  _exportChat();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'new_chat',
                child: Row(
                  children: [
                    Icon(Icons.add_comment),
                    SizedBox(width: 8),
                    Text('新对话'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'clear_history',
                child: Row(
                  children: [
                    Icon(Icons.clear_all),
                    SizedBox(width: 8),
                    Text('清空历史'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'export_chat',
                child: Row(
                  children: [
                    Icon(Icons.download),
                    SizedBox(width: 8),
                    Text('导出对话'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // 上下文信息栏
          if (_novel != null) _buildContextBar(),

          // 文件编辑预览区域
          _buildFileEditPreviewArea(),

          // 聊天消息列表
          Expanded(
            child: Obx(() => _buildMessageList()),
          ),

          // 输入框
          _buildInputArea(),
        ],
      ),
    );
  }

  /// 构建上下文信息栏
  Widget _buildContextBar() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.book,
            size: 16,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              '正在为《${_novel!.title}》提供写作建议',
              style: TextStyle(
                fontSize: 12,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ),
          if (_currentChapter != null) ...[
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                '第${_currentChapter!.number}章',
                style: TextStyle(
                  fontSize: 10,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建文件编辑预览区域
  Widget _buildFileEditPreviewArea() {
    return GetBuilder<AIFileEditorController>(
      builder: (fileController) {
        if (fileController.pendingEdits.isEmpty) {
          return const SizedBox.shrink();
        }

        return Container(
          constraints: const BoxConstraints(maxHeight: 300),
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: fileController.pendingEdits.length,
            itemBuilder: (context, index) {
              final pendingEdit = fileController.pendingEdits[index];
              return SizedBox(
                width: MediaQuery.of(context).size.width * 0.9,
                child: FileEditPreviewWidget(
                  pendingEdit: pendingEdit,
                  onApplied: () {
                    // 刷新界面
                    setState(() {});
                  },
                  onRejected: () {
                    // 刷新界面
                    setState(() {});
                  },
                ),
              );
            },
          ),
        );
      },
    );
  }

  /// 构建消息列表
  Widget _buildMessageList() {
    final session = _controller.currentSession.value;
    if (session == null || session.messages.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.smart_toy,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              '开始与 AI 写作助手对话',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '我可以帮助您：\n• 完善小说情节\n• 优化文字表达\n• 提供创作建议\n• 解答写作问题',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: session.messages.length,
      itemBuilder: (context, index) {
        final message = session.messages[index];
        return _buildMessageBubble(message);
      },
    );
  }

  /// 构建消息气泡
  Widget _buildMessageBubble(ChatMessage message) {
    final isUser = message.role == 'user';
    
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        mainAxisAlignment: isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isUser) ...[
            CircleAvatar(
              radius: 16,
              backgroundColor: Theme.of(context).colorScheme.primary,
              child: const Icon(
                Icons.smart_toy,
                size: 16,
                color: Colors.white,
              ),
            ),
            const SizedBox(width: 8),
          ],
          Flexible(
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: isUser
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.surfaceVariant,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    message.content,
                    style: TextStyle(
                      color: isUser
                          ? Theme.of(context).colorScheme.onPrimary
                          : Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _formatTime(message.timestamp),
                    style: TextStyle(
                      fontSize: 10,
                      color: isUser
                          ? Theme.of(context).colorScheme.onPrimary.withOpacity(0.7)
                          : Theme.of(context).colorScheme.onSurfaceVariant.withOpacity(0.7),
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (isUser) ...[
            const SizedBox(width: 8),
            CircleAvatar(
              radius: 16,
              backgroundColor: Theme.of(context).colorScheme.secondary,
              child: const Icon(
                Icons.person,
                size: 16,
                color: Colors.white,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建输入区域
  Widget _buildInputArea() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 0.5,
          ),
        ),
      ),
      child: Column(
        children: [
          // 当前编辑文件显示
          GetBuilder<AIFileEditorController>(
            builder: (fileController) {
              if (fileController.currentFilePath.value.isEmpty) {
                return const SizedBox.shrink();
              }

              return Container(
                padding: const EdgeInsets.all(8),
                margin: const EdgeInsets.only(bottom: 8),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primaryContainer,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.edit_document,
                      size: 16,
                      color: Theme.of(context).colorScheme.onPrimaryContainer,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        '正在编辑: ${fileController.currentFilePath.value.split('/').last}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onPrimaryContainer,
                        ),
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close, size: 16),
                      onPressed: () {
                        fileController.currentFilePath.value = '';
                        fileController.currentFileContent.value = '';
                      },
                      color: Theme.of(context).colorScheme.onPrimaryContainer,
                    ),
                  ],
                ),
              );
            },
          ),

          // 输入行
          Row(
            children: [
              // 文件选择按钮
              IconButton(
                icon: const Icon(Icons.attach_file),
                onPressed: _selectFileForEditing,
                tooltip: '选择要编辑的文件',
              ),

              // 输入框
              Expanded(
                child: TextField(
                  controller: _messageController,
                  maxLines: null,
                  decoration: InputDecoration(
                    hintText: '输入您的问题或编辑指令...',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                  onSubmitted: (_) => _sendMessage(),
                ),
              ),
              const SizedBox(width: 8),
              Obx(() => FloatingActionButton(
                    mini: true,
                    onPressed: _controller.isLoading.value ? null : _sendMessage,
                    child: _controller.isLoading.value
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.send),
                  )),
            ],
          ),
        ],
      ),
    );
  }

  /// 发送消息
  void _sendMessage() {
    final text = _messageController.text.trim();
    if (text.isEmpty) return;

    _messageController.clear();

    // 检查是否有当前编辑的文件
    final fileController = Get.find<AIFileEditorController>();
    final currentFilePath = fileController.currentFilePath.value;

    if (currentFilePath.isNotEmpty && _controller.isFileEditRequest(text)) {
      // 发送文件编辑请求
      _controller.sendFileEditMessage(
        content: text,
        filePath: currentFilePath,
        novel: _novel,
        chapter: _currentChapter,
      );
    } else {
      // 发送普通消息
      _controller.sendMessage(
        content: text,
        novel: _novel,
        chapter: _currentChapter,
      );
    }

    // 滚动到底部
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  /// 选择要编辑的文件
  void _selectFileForEditing() async {
    try {
      // 使用文件选择器
      const typeGroup = XTypeGroup(
        label: '文本文件',
        extensions: ['txt', 'md', 'dart', 'json', 'yaml', 'yml', 'html', 'css', 'js', 'py'],
      );

      final file = await openFile(
        acceptedTypeGroups: [typeGroup],
        confirmButtonText: '选择',
      );

      if (file != null) {
        final fileController = Get.find<AIFileEditorController>();
        final success = await fileController.openFile(file.path);
        if (success) {
          Get.snackbar('成功', '文件已打开: ${file.name}');
        }
      }
    } catch (e) {
      // 如果文件选择器不可用，回退到手动输入
      final result = await showDialog<String>(
        context: context,
        builder: (context) => _buildFilePathInputDialog(),
      );

      if (result != null && result.isNotEmpty) {
        final fileController = Get.find<AIFileEditorController>();
        final success = await fileController.openFile(result);
        if (success) {
          Get.snackbar('成功', '文件已打开');
        }
      }
    }
  }

  /// 构建文件路径输入对话框
  Widget _buildFilePathInputDialog() {
    final controller = TextEditingController();

    return AlertDialog(
      title: const Text('选择要编辑的文件'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text('请输入文件路径:'),
          const SizedBox(height: 8),
          TextField(
            controller: controller,
            decoration: const InputDecoration(
              hintText: '例如: /path/to/your/file.md',
              border: OutlineInputBorder(),
            ),
            autofocus: true,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('取消'),
        ),
        TextButton(
          onPressed: () => Navigator.pop(context, controller.text),
          child: const Text('确定'),
        ),
      ],
    );
  }

  /// 格式化时间
  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  /// 显示设置对话框
  void _showSettingsDialog() {
    Get.to(() => const SmartComposerSettingsScreen());
  }

  /// 开始新对话
  void _startNewChat() {
    final session = _controller.createNewSession(
      title: _novel != null ? '《${_novel!.title}》写作助手' : '新对话',
      novelId: _novel?.id,
      context: _novel != null ? {
        'novel_title': _novel!.title,
        'novel_genre': _novel!.genre,
        'novel_outline': _novel!.outline,
        'chapter_count': _novel!.chapters.length,
      } : null,
    );
    
    setState(() {
      _session = session;
    });
  }

  /// 清空聊天历史
  void _clearChatHistory() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('清空历史'),
        content: const Text('确定要清空当前对话的所有消息吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _startNewChat();
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  /// 导出对话
  void _exportChat() {
    // TODO: 实现导出对话功能
    Get.snackbar('提示', '导出功能正在开发中');
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }
}
