import '../models/smart_composer_models.dart';

/// AI配置验证工具
class AIConfigValidator {
  /// 验证LLM提供商配置
  static ValidationResult validateProvider(LLMProvider provider) {
    final errors = <String>[];
    final warnings = <String>[];

    // 检查API密钥
    if (provider.apiKey == null || provider.apiKey!.isEmpty) {
      errors.add('API密钥未配置');
    }

    // 检查基础URL
    if (provider.baseUrl == null || provider.baseUrl!.isEmpty) {
      warnings.add('基础URL未配置，将使用默认值');
    } else {
      // 验证URL格式
      try {
        final uri = Uri.parse(provider.baseUrl!);
        if (!uri.hasScheme || (!uri.scheme.startsWith('http'))) {
          errors.add('基础URL格式不正确，应以http://或https://开头');
        }
      } catch (e) {
        errors.add('基础URL格式无效: $e');
      }
    }

    // 检查提供商类型特定的配置
    switch (provider.type) {
      case LLMProviderType.openai:
        if (provider.baseUrl == null) {
          provider = provider.copyWith(baseUrl: 'https://api.openai.com/v1');
        }
        break;
      case LLMProviderType.anthropic:
        if (provider.baseUrl == null) {
          provider = provider.copyWith(baseUrl: 'https://api.anthropic.com');
        }
        break;
      case LLMProviderType.gemini:
        if (provider.baseUrl == null) {
          provider = provider.copyWith(baseUrl: 'https://generativelanguage.googleapis.com/v1beta');
        }
        break;
      case LLMProviderType.deepseek:
        if (provider.baseUrl == null) {
          provider = provider.copyWith(baseUrl: 'https://api.deepseek.com/v1');
        }
        break;
      default:
        break;
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// 验证聊天模型配置
  static ValidationResult validateChatModel(ChatModel model, LLMProvider provider) {
    final errors = <String>[];
    final warnings = <String>[];

    // 检查模型名称
    if (model.model.isEmpty) {
      errors.add('模型名称不能为空');
    }

    // 检查模型名称是否与提供商匹配
    switch (provider.type) {
      case LLMProviderType.openai:
        if (!_isValidOpenAIModel(model.model)) {
          warnings.add('模型名称可能不是有效的OpenAI模型');
        }
        break;
      case LLMProviderType.anthropic:
        if (!_isValidAnthropicModel(model.model)) {
          warnings.add('模型名称可能不是有效的Anthropic模型');
        }
        break;
      case LLMProviderType.gemini:
        if (!_isValidGeminiModel(model.model)) {
          warnings.add('模型名称可能不是有效的Gemini模型');
        }
        break;
      default:
        break;
    }

    // 检查最大令牌数
    if (model.maxTokens <= 0) {
      errors.add('最大令牌数必须大于0');
    } else if (model.maxTokens > 32000) {
      warnings.add('最大令牌数较大，可能导致API调用费用较高');
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// 验证完整的Smart Composer设置
  static ValidationResult validateSettings(SmartComposerSettings settings) {
    final errors = <String>[];
    final warnings = <String>[];

    // 检查是否有提供商
    if (settings.providers.isEmpty) {
      errors.add('至少需要配置一个LLM提供商');
      return ValidationResult(
        isValid: false,
        errors: errors,
        warnings: warnings,
      );
    }

    // 检查是否有聊天模型
    if (settings.chatModels.isEmpty) {
      errors.add('至少需要配置一个聊天模型');
      return ValidationResult(
        isValid: false,
        errors: errors,
        warnings: warnings,
      );
    }

    // 检查默认模型是否存在
    if (settings.defaultChatModelId != null) {
      final defaultModel = settings.chatModels
          .where((m) => m.id == settings.defaultChatModelId)
          .firstOrNull;
      if (defaultModel == null) {
        errors.add('默认聊天模型不存在');
      }
    } else {
      warnings.add('未设置默认聊天模型');
    }

    // 验证每个提供商
    for (final provider in settings.providers) {
      final providerResult = validateProvider(provider);
      errors.addAll(providerResult.errors.map((e) => '提供商${provider.name}: $e'));
      warnings.addAll(providerResult.warnings.map((w) => '提供商${provider.name}: $w'));
    }

    // 验证每个模型
    for (final model in settings.chatModels) {
      final provider = settings.providers
          .where((p) => p.id == model.providerId)
          .firstOrNull;
      if (provider == null) {
        errors.add('模型${model.name}的提供商不存在');
        continue;
      }

      final modelResult = validateChatModel(model, provider);
      errors.addAll(modelResult.errors.map((e) => '模型${model.name}: $e'));
      warnings.addAll(modelResult.warnings.map((w) => '模型${model.name}: $w'));
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// 检查是否为有效的OpenAI模型
  static bool _isValidOpenAIModel(String model) {
    final validModels = [
      'gpt-4', 'gpt-4-turbo', 'gpt-4-turbo-preview',
      'gpt-3.5-turbo', 'gpt-3.5-turbo-16k',
      'text-davinci-003', 'text-davinci-002',
    ];
    return validModels.any((valid) => model.startsWith(valid));
  }

  /// 检查是否为有效的Anthropic模型
  static bool _isValidAnthropicModel(String model) {
    final validModels = [
      'claude-3', 'claude-2', 'claude-instant',
    ];
    return validModels.any((valid) => model.startsWith(valid));
  }

  /// 检查是否为有效的Gemini模型
  static bool _isValidGeminiModel(String model) {
    final validModels = [
      'gemini-pro', 'gemini-pro-vision', 'gemini-1.5',
    ];
    return validModels.any((valid) => model.startsWith(valid));
  }

  /// 生成配置建议
  static List<String> generateConfigSuggestions(SmartComposerSettings settings) {
    final suggestions = <String>[];

    if (settings.providers.isEmpty) {
      suggestions.add('建议添加至少一个LLM提供商（如OpenAI、Anthropic等）');
    }

    if (settings.chatModels.isEmpty) {
      suggestions.add('建议添加至少一个聊天模型');
    }

    if (settings.systemPrompt == null || settings.systemPrompt!.isEmpty) {
      suggestions.add('建议设置系统提示以获得更好的AI响应');
    }

    // 检查是否有多个提供商作为备选
    if (settings.providers.length == 1) {
      suggestions.add('建议配置多个LLM提供商作为备选，提高可用性');
    }

    return suggestions;
  }
}

/// 验证结果
class ValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;

  ValidationResult({
    required this.isValid,
    required this.errors,
    required this.warnings,
  });

  bool get hasWarnings => warnings.isNotEmpty;
  bool get hasErrors => errors.isNotEmpty;
}
