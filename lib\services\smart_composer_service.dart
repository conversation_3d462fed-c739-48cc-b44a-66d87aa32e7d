import 'dart:async';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:uuid/uuid.dart';
import '../models/smart_composer_models.dart';
import '../models/novel.dart';

/// Smart Composer AI 服务
/// 提供与各种 LLM 提供商的集成
class SmartComposerService {
  static const _uuid = Uuid();
  
  /// 发送聊天消息
  Future<String> sendChatMessage({
    required ChatModel model,
    required LLMProvider provider,
    required List<ChatMessage> messages,
    String? systemPrompt,
    double temperature = 0.7,
    int maxTokens = 2000,
  }) async {
    switch (provider.type) {
      case LLMProviderType.openai:
        return _sendOpenAIMessage(
          provider: provider,
          model: model,
          messages: messages,
          systemPrompt: systemPrompt,
          temperature: temperature,
          maxTokens: maxTokens,
        );
      case LLMProviderType.anthropic:
        return _sendAnthropicMessage(
          provider: provider,
          model: model,
          messages: messages,
          systemPrompt: systemPrompt,
          temperature: temperature,
          maxTokens: maxTokens,
        );
      case LLMProviderType.gemini:
        return _sendGeminiMessage(
          provider: provider,
          model: model,
          messages: messages,
          systemPrompt: systemPrompt,
          temperature: temperature,
          maxTokens: maxTokens,
        );
      case LLMProviderType.deepseek:
        return _sendDeepSeekMessage(
          provider: provider,
          model: model,
          messages: messages,
          systemPrompt: systemPrompt,
          temperature: temperature,
          maxTokens: maxTokens,
        );
      case LLMProviderType.openaiCompatible:
      case LLMProviderType.openrouter:
      case LLMProviderType.groq:
      case LLMProviderType.perplexity:
      case LLMProviderType.mistral:
        // 这些提供商都使用 OpenAI 兼容的 API
        return _sendOpenAIMessage(
          provider: provider,
          model: model,
          messages: messages,
          systemPrompt: systemPrompt,
          temperature: temperature,
          maxTokens: maxTokens,
        );
      case LLMProviderType.ollama:
      case LLMProviderType.lmStudio:
        // 本地模型也使用 OpenAI 兼容的 API
        return _sendOpenAIMessage(
          provider: provider,
          model: model,
          messages: messages,
          systemPrompt: systemPrompt,
          temperature: temperature,
          maxTokens: maxTokens,
        );
      case LLMProviderType.azureOpenai:
        // Azure OpenAI 也使用 OpenAI 兼容的 API
        return _sendOpenAIMessage(
          provider: provider,
          model: model,
          messages: messages,
          systemPrompt: systemPrompt,
          temperature: temperature,
          maxTokens: maxTokens,
        );
      case LLMProviderType.morph:
        // Morph 也使用 OpenAI 兼容的 API
        return _sendOpenAIMessage(
          provider: provider,
          model: model,
          messages: messages,
          systemPrompt: systemPrompt,
          temperature: temperature,
          maxTokens: maxTokens,
        );
      default:
        throw UnsupportedError('不支持的提供商类型: ${provider.type}');
    }
  }

  /// OpenAI API 调用
  Future<String> _sendOpenAIMessage({
    required LLMProvider provider,
    required ChatModel model,
    required List<ChatMessage> messages,
    String? systemPrompt,
    double temperature = 0.7,
    int maxTokens = 2000,
  }) async {
    final apiKey = provider.apiKey;
    if (apiKey == null || apiKey.isEmpty) {
      throw Exception('OpenAI API Key 未配置');
    }

    final baseUrl = provider.baseUrl ?? 'https://api.openai.com/v1';
    final url = Uri.parse('$baseUrl/chat/completions');

    final requestMessages = <Map<String, dynamic>>[];
    
    // 添加系统提示
    if (systemPrompt != null && systemPrompt.isNotEmpty) {
      requestMessages.add({
        'role': 'system',
        'content': systemPrompt,
      });
    }

    // 添加对话消息
    for (final message in messages) {
      requestMessages.add({
        'role': message.role,
        'content': message.content,
      });
    }

    final requestBody = {
      'model': model.model,
      'messages': requestMessages,
      'temperature': temperature,
      'max_tokens': maxTokens,
    };

    final response = await http.post(
      url,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $apiKey',
      },
      body: json.encode(requestBody),
    );

    if (response.statusCode != 200) {
      throw Exception('OpenAI API 调用失败: ${response.statusCode} ${response.body}');
    }

    final responseData = json.decode(response.body) as Map<String, dynamic>;
    final choices = responseData['choices'] as List;
    
    if (choices.isEmpty) {
      throw Exception('OpenAI API 返回空响应');
    }

    final message = choices.first['message'] as Map<String, dynamic>;
    return message['content'] as String;
  }

  /// Anthropic API 调用
  Future<String> _sendAnthropicMessage({
    required LLMProvider provider,
    required ChatModel model,
    required List<ChatMessage> messages,
    String? systemPrompt,
    double temperature = 0.7,
    int maxTokens = 2000,
  }) async {
    final apiKey = provider.apiKey;
    if (apiKey == null || apiKey.isEmpty) {
      throw Exception('Anthropic API Key 未配置');
    }

    final baseUrl = provider.baseUrl ?? 'https://api.anthropic.com/v1';
    final url = Uri.parse('$baseUrl/messages');

    final requestMessages = <Map<String, dynamic>>[];
    
    // Anthropic 不支持系统消息在 messages 数组中，需要单独处理
    for (final message in messages) {
      if (message.role != 'system') {
        requestMessages.add({
          'role': message.role,
          'content': message.content,
        });
      }
    }

    final requestBody = {
      'model': model.model,
      'messages': requestMessages,
      'temperature': temperature,
      'max_tokens': maxTokens,
    };

    // 添加系统提示
    if (systemPrompt != null && systemPrompt.isNotEmpty) {
      requestBody['system'] = systemPrompt;
    }

    final response = await http.post(
      url,
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': apiKey,
        'anthropic-version': '2023-06-01',
      },
      body: json.encode(requestBody),
    );

    if (response.statusCode != 200) {
      throw Exception('Anthropic API 调用失败: ${response.statusCode} ${response.body}');
    }

    final responseData = json.decode(response.body) as Map<String, dynamic>;
    final content = responseData['content'] as List;
    
    if (content.isEmpty) {
      throw Exception('Anthropic API 返回空响应');
    }

    final textContent = content.first as Map<String, dynamic>;
    return textContent['text'] as String;
  }

  /// Google Gemini API 调用
  Future<String> _sendGeminiMessage({
    required LLMProvider provider,
    required ChatModel model,
    required List<ChatMessage> messages,
    String? systemPrompt,
    double temperature = 0.7,
    int maxTokens = 2000,
  }) async {
    final apiKey = provider.apiKey;
    if (apiKey == null || apiKey.isEmpty) {
      throw Exception('Gemini API Key 未配置');
    }

    final baseUrl = provider.baseUrl ?? 'https://generativelanguage.googleapis.com/v1beta';
    final url = Uri.parse('$baseUrl/models/${model.model}:generateContent?key=$apiKey');

    final contents = <Map<String, dynamic>>[];
    
    // 构建对话内容
    String fullPrompt = '';
    if (systemPrompt != null && systemPrompt.isNotEmpty) {
      fullPrompt += '$systemPrompt\n\n';
    }
    
    for (final message in messages) {
      if (message.role == 'user') {
        fullPrompt += 'User: ${message.content}\n';
      } else if (message.role == 'assistant') {
        fullPrompt += 'Assistant: ${message.content}\n';
      }
    }

    contents.add({
      'parts': [
        {'text': fullPrompt}
      ]
    });

    final requestBody = {
      'contents': contents,
      'generationConfig': {
        'temperature': temperature,
        'maxOutputTokens': maxTokens,
      },
    };

    final response = await http.post(
      url,
      headers: {
        'Content-Type': 'application/json',
      },
      body: json.encode(requestBody),
    );

    if (response.statusCode != 200) {
      throw Exception('Gemini API 调用失败: ${response.statusCode} ${response.body}');
    }

    final responseData = json.decode(response.body) as Map<String, dynamic>;
    final candidates = responseData['candidates'] as List;
    
    if (candidates.isEmpty) {
      throw Exception('Gemini API 返回空响应');
    }

    final candidate = candidates.first as Map<String, dynamic>;
    final content = candidate['content'] as Map<String, dynamic>;
    final parts = content['parts'] as List;
    
    if (parts.isEmpty) {
      throw Exception('Gemini API 返回空内容');
    }

    final part = parts.first as Map<String, dynamic>;
    return part['text'] as String;
  }

  /// DeepSeek API 调用
  Future<String> _sendDeepSeekMessage({
    required LLMProvider provider,
    required ChatModel model,
    required List<ChatMessage> messages,
    String? systemPrompt,
    double temperature = 0.7,
    int maxTokens = 2000,
  }) async {
    final apiKey = provider.apiKey;
    if (apiKey == null || apiKey.isEmpty) {
      throw Exception('DeepSeek API Key 未配置');
    }

    final baseUrl = provider.baseUrl ?? 'https://api.deepseek.com/v1';
    final url = Uri.parse('$baseUrl/chat/completions');

    final requestMessages = <Map<String, dynamic>>[];
    
    // 添加系统提示
    if (systemPrompt != null && systemPrompt.isNotEmpty) {
      requestMessages.add({
        'role': 'system',
        'content': systemPrompt,
      });
    }

    // 添加对话消息
    for (final message in messages) {
      requestMessages.add({
        'role': message.role,
        'content': message.content,
      });
    }

    final requestBody = {
      'model': model.model,
      'messages': requestMessages,
      'temperature': temperature,
      'max_tokens': maxTokens,
    };

    final response = await http.post(
      url,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $apiKey',
      },
      body: json.encode(requestBody),
    );

    if (response.statusCode != 200) {
      throw Exception('DeepSeek API 调用失败: ${response.statusCode} ${response.body}');
    }

    final responseData = json.decode(response.body) as Map<String, dynamic>;
    final choices = responseData['choices'] as List;
    
    if (choices.isEmpty) {
      throw Exception('DeepSeek API 返回空响应');
    }

    final message = choices.first['message'] as Map<String, dynamic>;
    return message['content'] as String;
  }

  /// 创建新的聊天消息
  ChatMessage createUserMessage(String content) {
    return ChatMessage(
      id: _uuid.v4(),
      role: 'user',
      content: content,
      timestamp: DateTime.now(),
    );
  }

  /// 创建助手回复消息
  ChatMessage createAssistantMessage(String content) {
    return ChatMessage(
      id: _uuid.v4(),
      role: 'assistant',
      content: content,
      timestamp: DateTime.now(),
    );
  }

  /// 为小说章节生成上下文提示
  String generateNovelContextPrompt(Novel novel, Chapter? currentChapter) {
    final buffer = StringBuffer();
    
    buffer.writeln('以下是当前小说的基本信息：');
    buffer.writeln('标题：${novel.title}');
    buffer.writeln('类型：${novel.genre}');
    if (novel.style?.isNotEmpty == true) {
      buffer.writeln('风格：${novel.style}');
    }
    buffer.writeln();
    
    if (novel.outline.isNotEmpty) {
      buffer.writeln('小说大纲：');
      buffer.writeln(novel.outline);
      buffer.writeln();
    }
    
    if (currentChapter != null) {
      buffer.writeln('当前章节：第${currentChapter.number}章 - ${currentChapter.title}');
      buffer.writeln();
    }
    
    // 添加前几章的内容作为上下文（限制长度）
    if (novel.chapters.isNotEmpty) {
      buffer.writeln('前面章节内容：');
      final contextChapters = currentChapter != null 
          ? novel.chapters.where((c) => c.number < currentChapter.number).take(3)
          : novel.chapters.take(3);
      
      for (final chapter in contextChapters) {
        buffer.writeln('第${chapter.number}章：${chapter.title}');
        final content = chapter.content.length > 500 
            ? '${chapter.content.substring(0, 500)}...'
            : chapter.content;
        buffer.writeln(content);
        buffer.writeln();
      }
    }
    
    return buffer.toString();
  }
}
