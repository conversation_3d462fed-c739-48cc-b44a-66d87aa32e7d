import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:novel_app/controllers/api_config_controller.dart';
import 'package:novel_app/controllers/novel_controller.dart';
import 'package:novel_app/models/embedding_model_config.dart';
import 'package:novel_app/screens/network_test_screen.dart';
import 'package:novel_app/widgets/themed_dropdown.dart';
import 'package:url_launcher/url_launcher.dart' as url_launcher;

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final ApiConfigController controller = Get.find<ApiConfigController>();
  final NovelController novelController = Get.find<NovelController>();

  // 文本编辑控制器
  final Map<String, TextEditingController> _textControllers = {};
  Worker? _modelChangeWorker;

  @override
  void initState() {
    super.initState();
    // 初始化控制器
    _initTextControllers();
  }

  @override
  void dispose() {
    // 取消监听器
    _modelChangeWorker?.dispose();
    // 释放所有控制器
    for (var controller in _textControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  // 初始化文本控制器
  void _initTextControllers() {
    final currentModel = controller.getCurrentModel();

    // 为每个模型字段创建控制器
    _textControllers['apiKey'] =
        TextEditingController(text: currentModel.apiKey);
    _textControllers['apiUrl'] =
        TextEditingController(text: currentModel.apiUrl);
    _textControllers['apiPath'] =
        TextEditingController(text: currentModel.apiPath);
    _textControllers['model'] = TextEditingController(text: currentModel.model);
    _textControllers['appId'] = TextEditingController(text: currentModel.appId);
    _textControllers['maxTokens'] =
        TextEditingController(text: currentModel.maxTokens.toString());
    _textControllers['proxyUrl'] =
        TextEditingController(text: currentModel.proxyUrl);
    _textControllers['timeout'] =
        TextEditingController(text: currentModel.timeout.toString());

    // 嵌入模型控制器
    _textControllers['embeddingApiKey'] =
        TextEditingController(text: controller.embeddingModel.value.apiKey);
    _textControllers['embeddingBaseUrl'] =
        TextEditingController(text: controller.embeddingModel.value.baseUrl);
    _textControllers['embeddingApiPath'] =
        TextEditingController(text: controller.embeddingModel.value.apiPath);
    _textControllers['embeddingModelName'] =
        TextEditingController(text: controller.embeddingModel.value.modelName);
    _textControllers['embeddingTopK'] = TextEditingController(
        text: controller.embeddingModel.value.topK.toString());
    _textControllers['embeddingProxyUrl'] =
        TextEditingController(text: controller.embeddingModel.value.proxyUrl);
    _textControllers['embeddingTimeout'] = TextEditingController(
        text: controller.embeddingModel.value.timeout.toString());
  }

  // 更新文本控制器
  void _updateTextControllers() {
    if (!mounted) return;

    final currentModel = controller.getCurrentModel();

    // 更新模型字段控制器
    try {
      _textControllers['apiKey']?.text = currentModel.apiKey;
      _textControllers['apiUrl']?.text = currentModel.apiUrl;
      _textControllers['apiPath']?.text = currentModel.apiPath;
      _textControllers['model']?.text = currentModel.model;
      _textControllers['appId']?.text = currentModel.appId;
      _textControllers['maxTokens']?.text = currentModel.maxTokens.toString();
      _textControllers['proxyUrl']?.text = currentModel.proxyUrl;
      _textControllers['timeout']?.text = currentModel.timeout.toString();
    } catch (e) {
      // 如果控制器已被释放，忽略错误
      print('更新文本控制器时出错: $e');
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 添加监听器，当选中的模型变化时更新文本控制器
    _modelChangeWorker ??= ever(controller.selectedModelId, (_) {
      if (mounted) {
        _updateTextControllers();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('设置'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showAddModelDialog(context, controller),
          ),
        ],
      ),
      body: DefaultTabController(
        length: 2,
        child: Column(
          children: [
            TabBar(
              tabs: const [
                Tab(text: '模型配置'),
                Tab(text: '模型列表'),
              ],
              labelColor: Theme.of(context).primaryColor,
              unselectedLabelColor: Colors.grey,
            ),
            Expanded(
              child: TabBarView(
                children: [
                  // 第一个标签页 - 原有的模型配置
                  SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          Card(
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      const Text(
                                        '模型设置',
                                        style: TextStyle(
                                          fontSize: 20,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      TextButton(
                                        onPressed: () =>
                                            controller.resetToDefaults(),
                                        child: const Text('重置默认配置'),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 16),
                                  Obx(() {
                                    // 创建一个模型名称到模型对象的映射，确保唯一性
                                    final Map<String, ModelConfig>
                                        uniqueModels = {};
                                    for (final model in controller.models) {
                                      uniqueModels[model.name] = model;
                                    }

                                    // 使用唯一的模型列表
                                    final uniqueModelsList =
                                        uniqueModels.values.toList();

                                    // 确保选中的模型存在于列表中
                                    String selectedValue =
                                        controller.selectedModelId.value;
                                    if (!uniqueModels
                                            .containsKey(selectedValue) &&
                                        uniqueModelsList.isNotEmpty) {
                                      selectedValue = uniqueModelsList[0].name;
                                      // 更新选中的模型
                                      Future.microtask(() => controller
                                          .updateSelectedModel(selectedValue));
                                    }

                                    return ThemedDropdownButtonFormField<
                                        String>(
                                      value: selectedValue,
                                      decoration: const InputDecoration(
                                        labelText: '选择模型',
                                        border: OutlineInputBorder(),
                                      ),
                                      items: uniqueModelsList
                                          .map((model) => DropdownMenuItem(
                                                value: model.name,
                                                child: SizedBox(
                                                  width: 300,
                                                  child: Row(
                                                    mainAxisSize:
                                                        MainAxisSize.min,
                                                    children: [
                                                      Expanded(
                                                          child:
                                                              Text(model.name)),
                                                      if (model.isCustom)
                                                        IconButton(
                                                          icon: const Icon(
                                                              Icons
                                                                  .delete_outline,
                                                              size: 20),
                                                          onPressed: () {
                                                            controller
                                                                .removeCustomModel(
                                                                    model.name);
                                                          },
                                                        ),
                                                    ],
                                                  ),
                                                ),
                                              ))
                                          .toList(),
                                      onChanged: (value) {
                                        if (value != null) {
                                          controller.updateSelectedModel(value);
                                        }
                                      },
                                    );
                                  }),
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(height: 16),
                          Obx(() {
                            final currentModel = controller.getCurrentModel();
                            return Card(
                              child: Padding(
                                padding: const EdgeInsets.all(16.0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      '${currentModel.name} 配置',
                                      style: const TextStyle(
                                        fontSize: 20,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    const SizedBox(height: 16),
                                    TextField(
                                      decoration: const InputDecoration(
                                        labelText: 'API Key',
                                        hintText: '请输入您的 API Key',
                                        border: OutlineInputBorder(),
                                      ),
                                      onChanged: (value) =>
                                          controller.updateModelConfig(
                                        currentModel.name,
                                        apiKey: value,
                                      ),
                                      controller: _textControllers['apiKey'],
                                    ),
                                    const SizedBox(height: 16),
                                    TextField(
                                      decoration: const InputDecoration(
                                        labelText: 'API URL',
                                        hintText: '请输入 API 服务器地址',
                                        border: OutlineInputBorder(),
                                      ),
                                      onChanged: (value) =>
                                          controller.updateModelConfig(
                                        currentModel.name,
                                        apiUrl: value,
                                      ),
                                      controller: _textControllers['apiUrl'],
                                    ),
                                    const SizedBox(height: 16),
                                    TextField(
                                      decoration: const InputDecoration(
                                        labelText: 'API 路径',
                                        hintText:
                                            '请输入 API 路径（如 /v1/chat/completions）',
                                        border: OutlineInputBorder(),
                                      ),
                                      onChanged: (value) =>
                                          controller.updateModelConfig(
                                        currentModel.name,
                                        apiPath: value,
                                      ),
                                      controller: _textControllers['apiPath'],
                                    ),
                                    const SizedBox(height: 16),
                                    // 添加阿里云通义千问深度思考模式设置
                                    if (currentModel.name.contains('阿里云') ||
                                        currentModel.name.contains('通义') ||
                                        currentModel.apiUrl
                                            .contains('dashscope.aliyuncs.com'))
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          const Text(
                                            '阿里云通义千问特殊设置',
                                            style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          const SizedBox(height: 8),
                                          SwitchListTile(
                                            title: const Text('启用深度思考模式'),
                                            subtitle: const Text(
                                                '使模型进行更深入的思考，生成更高质量的内容'),
                                            value: currentModel.enableThinking,
                                            onChanged: (value) =>
                                                controller.updateModelConfig(
                                              currentModel.name,
                                              enableThinking: value,
                                            ),
                                          ),
                                        ],
                                      ),

                                    // 添加代理设置选项
                                    if (currentModel.apiFormat == 'Google API')
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          const Text(
                                            '网络代理设置（可选）',
                                            style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          const SizedBox(height: 8),
                                          SwitchListTile(
                                            title: const Text('启用代理'),
                                            subtitle: const Text(
                                                '如果已有VPN或系统代理，通常无需启用'),
                                            value: currentModel.useProxy,
                                            onChanged: (value) =>
                                                controller.updateModelConfig(
                                              currentModel.name,
                                              useProxy: value,
                                            ),
                                          ),
                                          const SizedBox(height: 8),
                                          TextField(
                                            decoration: const InputDecoration(
                                              labelText: '代理服务器地址',
                                              hintText: '例如: 127.0.0.1:7890',
                                              border: OutlineInputBorder(),
                                            ),
                                            onChanged: (value) =>
                                                controller.updateModelConfig(
                                              currentModel.name,
                                              proxyUrl: value,
                                            ),
                                            controller:
                                                _textControllers['proxyUrl'],
                                          ),
                                          const SizedBox(height: 8),
                                          TextField(
                                            decoration: const InputDecoration(
                                              labelText: '请求超时时间(秒)',
                                              hintText: '建议设置为60-120秒',
                                              border: OutlineInputBorder(),
                                            ),
                                            keyboardType: TextInputType.number,
                                            onChanged: (value) =>
                                                controller.updateModelConfig(
                                              currentModel.name,
                                              timeout:
                                                  int.tryParse(value) ?? 60,
                                            ),
                                            controller:
                                                _textControllers['timeout'],
                                          ),
                                          const SizedBox(height: 16),
                                          // 添加网络测试按钮
                                          ElevatedButton.icon(
                                            icon:
                                                const Icon(Icons.network_check),
                                            label: const Text('网络连接诊断'),
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor: Colors.blue,
                                              foregroundColor: Colors.white,
                                            ),
                                            onPressed: () {
                                              Get.to(() =>
                                                  const NetworkTestScreen());
                                            },
                                          ),
                                        ],
                                      ),
                                    const SizedBox(height: 16),
                                    ElevatedButton(
                                      onPressed: () async {
                                        // 显示加载对话框
                                        showDialog(
                                          context: context,
                                          barrierDismissible: false,
                                          builder: (context) =>
                                              const AlertDialog(
                                            content: Column(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                CircularProgressIndicator(),
                                                SizedBox(height: 16),
                                                Text('正在验证连接...'),
                                              ],
                                            ),
                                          ),
                                        );

                                        // 验证连接
                                        final result = await controller
                                            .validateModelConnection(
                                          currentModel.name,
                                        );

                                        // 检查组件是否仍然挂载
                                        if (!mounted) return;

                                        // 关闭加载对话框
                                        Navigator.of(context).pop();

                                        // 显示结果
                                        showDialog(
                                          context: context,
                                          builder: (context) => AlertDialog(
                                            title: Text(
                                              result['success'] == true
                                                  ? '验证成功'
                                                  : '验证失败',
                                              style: TextStyle(
                                                color: result['success'] == true
                                                    ? Colors.green
                                                    : Colors.red,
                                              ),
                                            ),
                                            content: SingleChildScrollView(
                                              child: Column(
                                                mainAxisSize: MainAxisSize.min,
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(result['message'] ?? ''),
                                                  const SizedBox(height: 16),

                                                  // 如果有生成测试结果，显示生成的内容
                                                  if (result['generationTest'] !=
                                                          null &&
                                                      result['generationTest']
                                                              ['success'] ==
                                                          true)
                                                    Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        const Text('模型生成测试结果:',
                                                            style: TextStyle(
                                                                fontWeight:
                                                                    FontWeight
                                                                        .bold)),
                                                        const SizedBox(
                                                            height: 8),
                                                        Container(
                                                          padding:
                                                              const EdgeInsets
                                                                  .all(8),
                                                          decoration:
                                                              BoxDecoration(
                                                            color: Colors
                                                                .grey[200],
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        4),
                                                          ),
                                                          child: Column(
                                                            crossAxisAlignment:
                                                                CrossAxisAlignment
                                                                    .start,
                                                            children: [
                                                              Text(
                                                                  '生成内容: ${result['generationTest']['generatedText']}'),
                                                              Text(
                                                                  '响应时间: ${result['generationTest']['responseTime']}'),
                                                            ],
                                                          ),
                                                        ),
                                                      ],
                                                    ),

                                                  // 如果生成测试失败，显示错误信息
                                                  if (result['generationTest'] !=
                                                          null &&
                                                      result['generationTest']
                                                              ['success'] ==
                                                          false)
                                                    Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        const Text('模型生成测试失败:',
                                                            style: TextStyle(
                                                                fontWeight:
                                                                    FontWeight
                                                                        .bold,
                                                                color: Colors
                                                                    .red)),
                                                        const SizedBox(
                                                            height: 8),
                                                        Container(
                                                          padding:
                                                              const EdgeInsets
                                                                  .all(8),
                                                          decoration:
                                                              BoxDecoration(
                                                            color:
                                                                Colors.red[50],
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        4),
                                                          ),
                                                          child: Text(result[
                                                                      'generationTest']
                                                                  ['error'] ??
                                                              '未知错误'),
                                                        ),
                                                      ],
                                                    ),

                                                  // 如果有错误信息，显示错误详情
                                                  if (result['error'] != null)
                                                    Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        const SizedBox(
                                                            height: 16),
                                                        const Text('错误详情:',
                                                            style: TextStyle(
                                                                fontWeight:
                                                                    FontWeight
                                                                        .bold,
                                                                color: Colors
                                                                    .red)),
                                                        const SizedBox(
                                                            height: 8),
                                                        Container(
                                                          padding:
                                                              const EdgeInsets
                                                                  .all(8),
                                                          decoration:
                                                              BoxDecoration(
                                                            color:
                                                                Colors.red[50],
                                                            borderRadius:
                                                                BorderRadius
                                                                    .circular(
                                                                        4),
                                                          ),
                                                          child: Text(
                                                              result['error']),
                                                        ),
                                                      ],
                                                    ),
                                                ],
                                              ),
                                            ),
                                            actions: [
                                              TextButton(
                                                onPressed: () =>
                                                    Navigator.of(context).pop(),
                                                child: const Text('关闭'),
                                              ),
                                            ],
                                          ),
                                        );
                                      },
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor:
                                            Theme.of(context).primaryColor,
                                        foregroundColor: Colors.white,
                                      ),
                                      child: const Text('验证连接'),
                                    ),
                                    const SizedBox(height: 16),
                                    // 模型标识符组件
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          children: [
                                            Expanded(
                                              child: TextField(
                                                decoration:
                                                    const InputDecoration(
                                                  labelText: '模型标识符',
                                                  hintText: '请输入具体的模型名称',
                                                  border: OutlineInputBorder(),
                                                ),
                                                onChanged: (value) => controller
                                                    .updateModelConfig(
                                                  currentModel.name,
                                                  model: value,
                                                ),
                                                controller:
                                                    _textControllers['model'],
                                              ),
                                            ),
                                            IconButton(
                                              icon:
                                                  const Icon(Icons.add_circle),
                                              tooltip: '添加模型变体',
                                              onPressed: () {
                                                _showAddModelVariantDialog(
                                                    context,
                                                    controller,
                                                    currentModel);
                                              },
                                            ),
                                          ],
                                        ),
                                        if (currentModel
                                            .modelVariants.isNotEmpty) ...[
                                          const SizedBox(height: 8),
                                          Container(
                                            width: double.infinity,
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 12, vertical: 8),
                                            decoration: BoxDecoration(
                                              color: Colors.grey.shade100,
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                            ),
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  '可用的模型变体 (${currentModel.modelVariants.length})',
                                                  style: const TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                    fontSize: 14,
                                                  ),
                                                ),
                                                const SizedBox(height: 4),
                                                Wrap(
                                                  spacing: 6.0,
                                                  runSpacing: 6.0,
                                                  children: List.generate(
                                                    currentModel
                                                        .modelVariants.length,
                                                    (index) =>
                                                        _buildVariantChip(
                                                      context,
                                                      controller,
                                                      currentModel,
                                                      currentModel
                                                          .modelVariants[index],
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ],
                                    ),
                                    const SizedBox(height: 16),
                                    TextField(
                                      decoration: const InputDecoration(
                                        labelText: 'App ID',
                                        hintText: '请输入应用ID（百度千帆等需要）',
                                        border: OutlineInputBorder(),
                                      ),
                                      onChanged: (value) =>
                                          controller.updateModelConfig(
                                        currentModel.name,
                                        appId: value,
                                      ),
                                      controller: _textControllers['appId'],
                                    ),
                                    const SizedBox(height: 16),
                                    TextField(
                                      decoration: const InputDecoration(
                                        labelText: '每章字数限制',
                                        hintText:
                                            '建议设置在4000-8000之间，过短可能导致情节单薄，过长则生成较慢',
                                        border: OutlineInputBorder(),
                                      ),
                                      keyboardType: TextInputType.number,
                                      onChanged: (value) =>
                                          controller.updateModelConfig(
                                        currentModel.name,
                                        maxTokens: int.tryParse(value) ?? 5000,
                                      ),
                                      controller: _textControllers['maxTokens'],
                                    ),
                                    const SizedBox(height: 16),
                                    ThemedDropdownButtonFormField<String>(
                                      value: currentModel.apiFormat,
                                      decoration: const InputDecoration(
                                        labelText: 'API 格式',
                                        border: OutlineInputBorder(),
                                      ),
                                      items: const [
                                        DropdownMenuItem(
                                          value: 'OpenAI API兼容',
                                          child: Text('OpenAI API兼容'),
                                        ),
                                        DropdownMenuItem(
                                          value: 'Google API',
                                          child: Text('Google API'),
                                        ),
                                      ],
                                      onChanged: (value) {
                                        if (value != null) {
                                          controller.updateModelConfig(
                                            currentModel.name,
                                            apiFormat: value,
                                          );
                                        }
                                      },
                                    ),
                                    const SizedBox(height: 24),
                                    const Text(
                                      '高级设置',
                                      style: TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    const SizedBox(height: 16),
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        const Text('温度 (Temperature)'),
                                        const SizedBox(height: 8),
                                        Row(
                                          children: [
                                            Expanded(
                                              child: Slider(
                                                value: controller
                                                    .temperature.value,
                                                min: 0.0,
                                                max: 2.0,
                                                divisions: 20,
                                                label: controller
                                                    .temperature.value
                                                    .toStringAsFixed(1),
                                                onChanged: (value) => controller
                                                    .updateTemperature(value),
                                              ),
                                            ),
                                            SizedBox(
                                              width: 60,
                                              child: Text(
                                                controller.temperature.value
                                                    .toStringAsFixed(1),
                                                textAlign: TextAlign.center,
                                              ),
                                            ),
                                          ],
                                        ),
                                        const SizedBox(height: 16),
                                        const Text('Top P'),
                                        const SizedBox(height: 8),
                                        Row(
                                          children: [
                                            Expanded(
                                              child: Slider(
                                                value: controller.topP.value,
                                                min: 0.0,
                                                max: 1.0,
                                                divisions: 10,
                                                label: controller.topP.value
                                                    .toStringAsFixed(1),
                                                onChanged: (value) => controller
                                                    .updateTopP(value),
                                              ),
                                            ),
                                            SizedBox(
                                              width: 60,
                                              child: Text(
                                                controller.topP.value
                                                    .toStringAsFixed(1),
                                                textAlign: TextAlign.center,
                                              ),
                                            ),
                                          ],
                                        ),
                                        const SizedBox(height: 16),
                                        const Text('重复惩罚 (Repetition Penalty)'),
                                        const Text(
                                          '控制文本重复的程度，值越大越不容易重复\n建议范围：1.0-1.5，默认1.3',
                                          style: TextStyle(
                                              fontSize: 12, color: Colors.grey),
                                        ),
                                        const SizedBox(height: 8),
                                        Row(
                                          children: [
                                            Expanded(
                                              child: Slider(
                                                value: controller
                                                    .repetitionPenalty.value,
                                                min: 1.0,
                                                max: 2.0,
                                                divisions: 20,
                                                label: controller
                                                    .repetitionPenalty.value
                                                    .toStringAsFixed(2),
                                                onChanged: (value) => controller
                                                    .updateRepetitionPenalty(
                                                        value),
                                              ),
                                            ),
                                            SizedBox(
                                              width: 60,
                                              child: Text(
                                                controller
                                                    .repetitionPenalty.value
                                                    .toStringAsFixed(2),
                                                textAlign: TextAlign.center,
                                              ),
                                            ),
                                          ],
                                        ),
                                        const SizedBox(height: 16),
                                        const Text('最大生成长度'),
                                        const Text(
                                          '控制每章生成的最大长度，建议4000-8000之间\n数值越大生成内容越长，但速度也越慢',
                                          style: TextStyle(
                                              fontSize: 12, color: Colors.grey),
                                        ),
                                        const SizedBox(height: 8),
                                        Row(
                                          children: [
                                            Expanded(
                                              child: Slider(
                                                value: currentModel.maxTokens
                                                    .toDouble(),
                                                min: 2000,
                                                max: 16384,
                                                divisions: 144,
                                                label:
                                                    '${currentModel.maxTokens} tokens',
                                                onChanged: (value) {
                                                  final tokens = value.toInt();
                                                  controller
                                                      .updateMaxTokens(tokens);
                                                  controller.updateModelConfig(
                                                    currentModel.name,
                                                    maxTokens: tokens,
                                                  );
                                                },
                                              ),
                                            ),
                                            SizedBox(
                                              width: 100,
                                              child: Text(
                                                '${currentModel.maxTokens}\ntokens',
                                                textAlign: TextAlign.center,
                                                style: const TextStyle(
                                                    height: 1.2),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            );
                          }),

                          // 添加双模型模式区域
                          const SizedBox(height: 16),
                          const Card(
                            child: Padding(
                              padding: EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    '双模型模式',
                                    style: TextStyle(
                                      fontSize: 20,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  SizedBox(height: 8),
                                  Text(
                                    '开启后可分别选择用于大纲和章节生成的模型，实现高性能大纲生成和稳定章节生成',
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Colors.grey,
                                    ),
                                  ),
                                  SizedBox(height: 16),
                                  // 移除双模型模式开关
                                  // Obx(() => SwitchListTile(
                                  //   title: const Text('启用双模型模式'),
                                  //   subtitle: const Text('分别为大纲和章节选择不同模型'),
                                  //   value: controller.isDualModelMode.value,
                                  //   onChanged: (value) {
                                  //     controller.isDualModelMode.value = value;
                                  //     controller.saveDualModelConfig();
                                  //   },
                                  // )),

                                  // 移除双模型模式相关UI
                                  // Obx(() => controller.isDualModelMode.value
                                  // ? Column(
                                  //     crossAxisAlignment: CrossAxisAlignment.start,
                                  //     children: [
                                  //       const SizedBox(height: 16),
                                  //       const Text(
                                  //         '大纲生成模型',
                                  //         style: TextStyle(
                                  //           fontSize: 16,
                                  //           fontWeight: FontWeight.bold,
                                  //         ),
                                  //       ),
                                  //       const SizedBox(height: 8),
                                  //       DropdownButtonFormField<String>(
                                  //         value: controller.outlineModelId.value.isEmpty
                                  //             ? controller.selectedModelId.value
                                  //             : controller.outlineModelId.value,
                                  //         decoration: const InputDecoration(
                                  //           labelText: '选择大纲生成模型',
                                  //           helperText: '推荐选择高性能模型，如GPT-4或Deepseek-Reasoner',
                                  //           border: OutlineInputBorder(),
                                  //         ),
                                  //         items: controller.models.map((model) => DropdownMenuItem(
                                  //           value: model.name,
                                  //           child: Text(model.name),
                                  //         )).toList(),
                                  //         onChanged: (value) {
                                  //           if (value != null) {
                                  //             controller.outlineModelId.value = value;
                                  //             controller.outlineModelVariant.value = ''; // 清空之前的变体选择
                                  //             controller.saveDualModelConfig();
                                  //           }
                                  //         },
                                  //       ),
                                  //
                                  //       // 添加大纲模型变体选择
                                  //       Obx(() {
                                  //         // 获取当前选择的大纲模型
                                  //         final outlineModel = controller.models.firstWhere(
                                  //           (model) => model.name == (controller.outlineModelId.value.isEmpty
                                  //               ? controller.selectedModelId.value
                                  //               : controller.outlineModelId.value),
                                  //           orElse: () => controller.models.first,
                                  //         );
                                  //
                                  //         // 如果该模型有变体，显示变体选择
                                  //         if (outlineModel.modelVariants.isNotEmpty) {
                                  //           return Padding(
                                  //             padding: const EdgeInsets.only(top: 16.0),
                                  //             child: DropdownButtonFormField<String>(
                                  //               value: controller.outlineModelVariant.value.isEmpty
                                  //                   ? outlineModel.model  // 默认使用模型当前值
                                  //                   : controller.outlineModelVariant.value,
                                  //               decoration: const InputDecoration(
                                  //                 labelText: '选择大纲模型变体',
                                  //                 helperText: '不同的模型变体可能有不同的性能和特点',
                                  //                 border: OutlineInputBorder(),
                                  //               ),
                                  //               items: [
                                  //                 DropdownMenuItem(
                                  //                   value: outlineModel.model,
                                  //                   child: Text('${outlineModel.model} (默认)'),
                                  //                 ),
                                  //                 ...outlineModel.modelVariants.map((variant) => DropdownMenuItem(
                                  //                   value: variant,
                                  //                   child: Text(variant),
                                  //                 )).toList(),
                                  //               ],
                                  //               onChanged: (value) {
                                  //                 if (value != null) {
                                  //                   controller.outlineModelVariant.value = value;
                                  //                   controller.saveDualModelConfig();
                                  //                 }
                                  //               },
                                  //             ),
                                  //           );
                                  //         }
                                  //         return const SizedBox.shrink();
                                  //       }),
                                  //
                                  //       const SizedBox(height: 24),
                                  //       const Text(
                                  //         '章节生成模型',
                                  //         style: TextStyle(
                                  //           fontSize: 16,
                                  //           fontWeight: FontWeight.bold,
                                  //         ),
                                  //       ),
                                  //       const SizedBox(height: 8),
                                  //       DropdownButtonFormField<String>(
                                  //         value: controller.chapterModelId.value.isEmpty
                                  //             ? controller.selectedModelId.value
                                  //             : controller.chapterModelId.value,
                                  //         decoration: const InputDecoration(
                                  //           labelText: '选择章节生成模型',
                                  //           helperText: '推荐选择稳定性高的模型，如Qwen或GPT-3.5',
                                  //           border: OutlineInputBorder(),
                                  //         ),
                                  //         items: controller.models.map((model) => DropdownMenuItem(
                                  //           value: model.name,
                                  //           child: Text(model.name),
                                  //         )).toList(),
                                  //         onChanged: (value) {
                                  //           if (value != null) {
                                  //             controller.chapterModelId.value = value;
                                  //             controller.chapterModelVariant.value = ''; // 清空之前的变体选择
                                  //             controller.saveDualModelConfig();
                                  //           }
                                  //         },
                                  //       ),
                                  //
                                  //       // 添加章节模型变体选择
                                  //       Obx(() {
                                  //         // 获取当前选择的章节模型
                                  //         final chapterModel = controller.models.firstWhere(
                                  //           (model) => model.name == (controller.chapterModelId.value.isEmpty
                                  //               ? controller.selectedModelId.value
                                  //               : controller.chapterModelId.value),
                                  //           orElse: () => controller.models.first,
                                  //         );
                                  //
                                  //         // 如果该模型有变体，显示变体选择
                                  //         if (chapterModel.modelVariants.isNotEmpty) {
                                  //           return Padding(
                                  //             padding: const EdgeInsets.only(top: 16.0),
                                  //             child: DropdownButtonFormField<String>(
                                  //               value: controller.chapterModelVariant.value.isEmpty
                                  //                   ? chapterModel.model  // 默认使用模型当前值
                                  //                   : controller.chapterModelVariant.value,
                                  //               decoration: const InputDecoration(
                                  //                 labelText: '选择章节模型变体',
                                  //                 helperText: '不同的模型变体可能有不同的稳定性和生成风格',
                                  //                 border: OutlineInputBorder(),
                                  //               ),
                                  //               items: [
                                  //                 DropdownMenuItem(
                                  //                   value: chapterModel.model,
                                  //                   child: Text('${chapterModel.model} (默认)'),
                                  //                 ),
                                  //                 ...chapterModel.modelVariants.map((variant) => DropdownMenuItem(
                                  //                   value: variant,
                                  //                   child: Text(variant),
                                  //                 )).toList(),
                                  //               ],
                                  //               onChanged: (value) {
                                  //                 if (value != null) {
                                  //                   controller.chapterModelVariant.value = value;
                                  //                   controller.saveDualModelConfig();
                                  //                 }
                                  //               },
                                  //             ),
                                  //           );
                                  //         }
                                  //         return const SizedBox.shrink();
                                  //       }),
                                  //     ],
                                  //   )
                                  //   : const SizedBox.shrink()
                                  // ),
                                ],
                              ),
                            ),
                          ),
                          // 添加嵌入模型配置卡片
                          const SizedBox(height: 16),
                          Obx(() => Card(
                                child: Padding(
                                  padding: const EdgeInsets.all(16.0),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      const Text(
                                        '嵌入模型配置',
                                        style: TextStyle(
                                          fontSize: 20,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      const SizedBox(height: 8),
                                      const Text(
                                        '启用嵌入模型可以提高章节生成的连贯性，避免重复内容',
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: Colors.grey,
                                        ),
                                      ),
                                      const SizedBox(height: 16),
                                      SwitchListTile(
                                        title: const Text('启用嵌入模型'),
                                        subtitle: const Text('使用嵌入模型改善章节生成质量'),
                                        value: controller
                                            .embeddingModel.value.enabled,
                                        onChanged: (value) {
                                          controller.updateEmbeddingModel(
                                              enabled: value);
                                        },
                                      ),
                                      const SizedBox(height: 16),

                                      // 嵌入模型选择
                                      const Text(
                                        '选择嵌入模型类型',
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      const SizedBox(height: 8),
                                      Row(
                                        children: [
                                          Expanded(
                                            child: RadioListTile<bool>(
                                              title: const Text('岱宗官方（限时免费）'),
                                              value: true,
                                              groupValue: controller
                                                      .embeddingModel
                                                      .value
                                                      .apiKey ==
                                                  'daizong_official_embedding_key',
                                              onChanged: (value) {
                                                if (value == true) {
                                                  // 切换到岱宗官方嵌入模型
                                                  controller
                                                      .updateEmbeddingModel(
                                                    name: '岱宗官方嵌入模型（限时免费）',
                                                    apiKey:
                                                        'daizong_official_embedding_key',
                                                    baseUrl:
                                                        'https://dashscope.aliyuncs.com',
                                                    apiPath:
                                                        '/compatible-mode/v1/embeddings',
                                                    modelName:
                                                        'text-embedding-v3',
                                                    apiFormat: 'OpenAI API兼容',
                                                    enabled: true,
                                                  );
                                                }
                                              },
                                            ),
                                          ),
                                          Expanded(
                                            child: RadioListTile<bool>(
                                              title: const Text('自定义模型'),
                                              value: false,
                                              groupValue: controller
                                                      .embeddingModel
                                                      .value
                                                      .apiKey ==
                                                  'daizong_official_embedding_key',
                                              onChanged: (value) {
                                                if (value == false) {
                                                  // 切换到自定义嵌入模型
                                                  final customModel =
                                                      EmbeddingModelConfig
                                                          .getCustom();
                                                  controller
                                                      .updateEmbeddingModel(
                                                    name: customModel.name,
                                                    apiKey: customModel.apiKey,
                                                    baseUrl:
                                                        customModel.baseUrl,
                                                    apiPath:
                                                        customModel.apiPath,
                                                    modelName:
                                                        customModel.modelName,
                                                    apiFormat:
                                                        customModel.apiFormat,
                                                  );
                                                }
                                              },
                                            ),
                                          ),
                                        ],
                                      ),

                                      const SizedBox(height: 16),
                                      const Divider(),
                                      const SizedBox(height: 16),

                                      // 只有在选择自定义模型时才显示这些字段
                                      Visibility(
                                        visible: controller
                                                .embeddingModel.value.apiKey !=
                                            'daizong_official_embedding_key',
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            const Text(
                                              '自定义模型设置',
                                              style: TextStyle(
                                                fontSize: 16,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                            const SizedBox(height: 16),
                                            TextField(
                                              decoration: const InputDecoration(
                                                labelText: '嵌入模型名称',
                                                hintText: '请输入嵌入模型名称',
                                                border: OutlineInputBorder(),
                                              ),
                                              onChanged: (value) => controller
                                                  .updateEmbeddingModel(
                                                      name: value),
                                              controller: TextEditingController(
                                                text: controller
                                                    .embeddingModel.value.name,
                                              ),
                                            ),
                                            const SizedBox(height: 16),
                                            TextField(
                                              decoration: const InputDecoration(
                                                labelText: 'API Key',
                                                hintText: '请输入嵌入模型的 API Key',
                                                border: OutlineInputBorder(),
                                              ),
                                              onChanged: (value) => controller
                                                  .updateEmbeddingModel(
                                                      apiKey: value),
                                              controller: TextEditingController(
                                                text: controller.embeddingModel
                                                            .value.apiKey ==
                                                        'daizong_official_embedding_key'
                                                    ? ''
                                                    : controller.embeddingModel
                                                        .value.apiKey,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      const SizedBox(height: 16),
                                      TextField(
                                        decoration: const InputDecoration(
                                          labelText: '基础 URL',
                                          hintText: '请输入嵌入模型的基础 URL',
                                          border: OutlineInputBorder(),
                                        ),
                                        onChanged: (value) =>
                                            controller.updateEmbeddingModel(
                                                baseUrl: value),
                                        controller: TextEditingController(
                                          text: controller
                                              .embeddingModel.value.baseUrl,
                                        ),
                                      ),
                                      const SizedBox(height: 16),
                                      TextField(
                                        decoration: const InputDecoration(
                                          labelText: 'API 路径',
                                          hintText: '请输入嵌入模型的 API 路径',
                                          border: OutlineInputBorder(),
                                        ),
                                        onChanged: (value) =>
                                            controller.updateEmbeddingModel(
                                                apiPath: value),
                                        controller: TextEditingController(
                                          text: controller
                                              .embeddingModel.value.apiPath,
                                        ),
                                      ),
                                      const SizedBox(height: 16),
                                      TextField(
                                        decoration: const InputDecoration(
                                          labelText: '模型名称',
                                          hintText: '请输入嵌入模型的模型名称',
                                          border: OutlineInputBorder(),
                                        ),
                                        onChanged: (value) =>
                                            controller.updateEmbeddingModel(
                                                modelName: value),
                                        controller: TextEditingController(
                                          text: controller
                                              .embeddingModel.value.modelName,
                                        ),
                                      ),
                                      const SizedBox(height: 16),
                                      ThemedDropdownButtonFormField<String>(
                                        value: controller
                                            .embeddingModel.value.apiFormat,
                                        decoration: const InputDecoration(
                                          labelText: 'API 格式',
                                          border: OutlineInputBorder(),
                                        ),
                                        items: const [
                                          DropdownMenuItem(
                                            value: 'OpenAI API兼容',
                                            child: Text('OpenAI API兼容'),
                                          ),
                                          DropdownMenuItem(
                                            value: 'Google API',
                                            child: Text('Google API'),
                                          ),
                                          DropdownMenuItem(
                                            value: '阿里百炼',
                                            child: Text('阿里百炼'),
                                          ),
                                        ],
                                        onChanged: (value) {
                                          if (value != null) {
                                            controller.updateEmbeddingModel(
                                                apiFormat: value);
                                          }
                                        },
                                      ),
                                      const SizedBox(height: 16),
                                      Row(
                                        children: [
                                          Expanded(
                                            child: Slider(
                                              value: controller
                                                  .embeddingModel.value.topK
                                                  .toDouble(),
                                              min: 1,
                                              max: 20,
                                              divisions: 19,
                                              label: controller
                                                  .embeddingModel.value.topK
                                                  .toString(),
                                              onChanged: (value) {
                                                controller.updateEmbeddingModel(
                                                    topK: value.toInt());
                                              },
                                            ),
                                          ),
                                          SizedBox(
                                            width: 60,
                                            child: Text(
                                              controller
                                                  .embeddingModel.value.topK
                                                  .toString(),
                                              textAlign: TextAlign.center,
                                            ),
                                          ),
                                        ],
                                      ),
                                      const Text('检索返回文档数量 (Top-K)',
                                          style: TextStyle(fontSize: 14)),

                                      // 添加代理设置
                                      const SizedBox(height: 16),
                                      const Divider(),
                                      const SizedBox(height: 8),
                                      const Text('连接设置',
                                          style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.bold)),
                                      const SizedBox(height: 16),

                                      // 代理开关
                                      SwitchListTile(
                                        title: const Text('使用代理'),
                                        subtitle:
                                            const Text('如果无法直接访问 API，可以尝试使用代理'),
                                        value: controller
                                            .embeddingModel.value.useProxy,
                                        onChanged: (value) {
                                          controller.updateEmbeddingModel(
                                              useProxy: value);
                                        },
                                      ),

                                      // 代理服务器设置
                                      Obx(() => controller
                                              .embeddingModel.value.useProxy
                                          ? Padding(
                                              padding: const EdgeInsets.only(
                                                  top: 8.0),
                                              child: TextField(
                                                decoration:
                                                    const InputDecoration(
                                                  labelText: '代理服务器',
                                                  hintText:
                                                      '输入代理服务器地址，如 127.0.0.1:7890',
                                                  border: OutlineInputBorder(),
                                                ),
                                                onChanged: (value) => controller
                                                    .updateEmbeddingModel(
                                                        proxyUrl: value),
                                                controller:
                                                    TextEditingController(
                                                  text: controller
                                                      .embeddingModel
                                                      .value
                                                      .proxyUrl,
                                                ),
                                              ),
                                            )
                                          : const SizedBox.shrink()),

                                      // 超时设置
                                      const SizedBox(height: 16),
                                      Row(
                                        children: [
                                          const Text('请求超时时间: '),
                                          Expanded(
                                            child: Slider(
                                              value: controller
                                                  .embeddingModel.value.timeout
                                                  .toDouble(),
                                              min: 5,
                                              max: 120,
                                              divisions: 23,
                                              label:
                                                  '${controller.embeddingModel.value.timeout}秒',
                                              onChanged: (value) {
                                                controller.updateEmbeddingModel(
                                                    timeout: value.toInt());
                                              },
                                            ),
                                          ),
                                          SizedBox(
                                            width: 60,
                                            child: Text(
                                              '${controller.embeddingModel.value.timeout}秒',
                                              textAlign: TextAlign.center,
                                            ),
                                          ),
                                        ],
                                      ),

                                      // 添加验证按钮
                                      const SizedBox(height: 24),
                                      ElevatedButton.icon(
                                        icon: const Icon(Icons.check_circle),
                                        label: const Text('验证连接'),
                                        style: ElevatedButton.styleFrom(
                                          minimumSize:
                                              const Size(double.infinity, 48),
                                        ),
                                        onPressed: () async {
                                          // 显示加载对话框
                                          Get.dialog(
                                            const Center(
                                              child:
                                                  CircularProgressIndicator(),
                                            ),
                                            barrierDismissible: false,
                                          );

                                          try {
                                            // 验证连接
                                            final result = await controller
                                                .validateEmbeddingConnection();

                                            // 关闭加载对话框
                                            Get.back();

                                            // 显示结果
                                            Get.dialog(
                                              AlertDialog(
                                                title: Text(
                                                  result['success']
                                                      ? '验证成功'
                                                      : '验证失败',
                                                  style: TextStyle(
                                                    color: result['success']
                                                        ? Colors.green
                                                        : Colors.red,
                                                  ),
                                                ),
                                                content:
                                                    Text(result['message']),
                                                actions: [
                                                  TextButton(
                                                    onPressed: () => Get.back(),
                                                    child: const Text('关闭'),
                                                  ),
                                                  if (result['success'])
                                                    TextButton(
                                                      onPressed: () {
                                                        Get.back();
                                                        // 如果验证成功，自动启用嵌入模型
                                                        if (!controller
                                                            .embeddingModel
                                                            .value
                                                            .enabled) {
                                                          controller
                                                              .updateEmbeddingModel(
                                                                  enabled:
                                                                      true);
                                                          Get.snackbar(
                                                              '提示', '嵌入模型已启用');
                                                        }
                                                      },
                                                      child:
                                                          const Text('启用嵌入模型'),
                                                    ),
                                                  // 如果验证失败且错误中包含关于API密钥的信息，显示获取API密钥的按钮
                                                  if (!result['success'] &&
                                                      (result['message']
                                                              .toString()
                                                              .contains(
                                                                  'API密钥') ||
                                                          result['message']
                                                              .toString()
                                                              .contains(
                                                                  '401') ||
                                                          result['message']
                                                              .toString()
                                                              .contains(
                                                                  'UNAUTHENTICATED')))
                                                    TextButton(
                                                      onPressed: () async {
                                                        // 打开Google AI Studio的API密钥页面
                                                        final Uri url = Uri.parse(
                                                            'https://makersuite.google.com/app/apikey');
                                                        try {
                                                          await url_launcher.launchUrl(
                                                              url,
                                                              mode: url_launcher
                                                                  .LaunchMode
                                                                  .externalApplication);
                                                        } catch (e) {
                                                          Get.snackbar('打开页面失败',
                                                              '无法打开浏览器: $e');
                                                        }
                                                      },
                                                      child:
                                                          const Text('获取API密钥'),
                                                    ),
                                                ],
                                              ),
                                            );
                                          } catch (e) {
                                            // 关闭加载对话框
                                            Get.back();

                                            // 显示错误
                                            Get.snackbar('验证失败', e.toString(),
                                                backgroundColor:
                                                    Colors.red.withAlpha(25),
                                                duration:
                                                    const Duration(seconds: 3));
                                          }
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                              )),

                          // 在所有配置卡片下方添加清除缓存按钮
                          const SizedBox(height: 16),
                          Card(
                            child: ListTile(
                              leading: const Icon(Icons.delete_sweep_outlined,
                                  color: Colors.red),
                              title: const Text('清除应用缓存'),
                              subtitle: const Text('清除所有本地缓存和会话数据'),
                              onTap: () {
                                // 显示确认对话框
                                Get.dialog(
                                  AlertDialog(
                                    title: const Text('确认清除缓存？'),
                                    content: const Text(
                                        '这将清除所有本地保存的小说生成历史、会话数据和其他缓存。此操作不可恢复。'),
                                    actions: [
                                      TextButton(
                                        child: const Text('取消'),
                                        onPressed: () => Get.back(),
                                      ),
                                      TextButton(
                                        child: const Text('确认清除',
                                            style:
                                                TextStyle(color: Colors.red)),
                                        onPressed: () {
                                          Get.back(); // 关闭对话框
                                          // 调用正确的清除缓存方法，无需 await
                                          novelController.clearCache();
                                        },
                                      ),
                                    ],
                                  ),
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  // 第二个标签页 - 模型列表
                  SingleChildScrollView(
                    padding: const EdgeInsets.all(16.0),
                    child: Obx(() => Column(
                          children: List.generate(
                              controller.models.length,
                              (index) => _buildModelDetailCard(
                                  context, controller, index)),
                        )),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 创建模型变体芯片
  Widget _buildVariantChip(BuildContext context, ApiConfigController controller,
      ModelConfig model, String variant) {
    final bool isCurrentModel = variant == model.model;

    return Chip(
      backgroundColor: isCurrentModel
          ? Theme.of(context).primaryColor.withOpacity(0.2)
          : Colors.grey.shade200,
      label: Text(
        variant,
        style: TextStyle(
          color:
              isCurrentModel ? Theme.of(context).primaryColor : Colors.black87,
          fontWeight: isCurrentModel ? FontWeight.bold : FontWeight.normal,
        ),
      ),
      deleteIcon: Icon(
        isCurrentModel ? Icons.check : Icons.close,
        size: 18,
        color: isCurrentModel ? Theme.of(context).primaryColor : Colors.black54,
      ),
      onDeleted: () {
        if (isCurrentModel) {
          // 如果是当前使用的模型变体，只显示勾号不做操作
          return;
        }
        // 确认是否删除变体
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('确认删除'),
            content: Text('确定要删除模型变体 "$variant" 吗？'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('取消'),
              ),
              TextButton(
                onPressed: () {
                  controller.removeModelVariant(model.name, variant);
                  Navigator.of(context).pop();
                },
                child: const Text('删除'),
              ),
            ],
          ),
        );
      },
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
    );
  }

  void _showAddModelDialog(
      BuildContext context, ApiConfigController controller) {
    final nameController = TextEditingController();
    final apiKeyController = TextEditingController();
    final apiUrlController = TextEditingController();
    final apiPathController = TextEditingController();
    final modelController = TextEditingController();
    final appIdController = TextEditingController();
    final variantController = TextEditingController();
    String selectedApiFormat = 'OpenAI API兼容';

    List<String> modelVariants = [];

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(builder: (context, setState) {
        return AlertDialog(
          title: const Text('添加自定义模型'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: nameController,
                  decoration: const InputDecoration(
                    labelText: '模型名称',
                    hintText: '请输入模型显示名称',
                  ),
                ),
                const SizedBox(height: 8),
                TextField(
                  controller: apiKeyController,
                  decoration: const InputDecoration(
                    labelText: 'API Key',
                    hintText: '请输入 API Key',
                  ),
                ),
                const SizedBox(height: 8),
                TextField(
                  controller: apiUrlController,
                  decoration: const InputDecoration(
                    labelText: 'API URL',
                    hintText: '请输入 API 服务器地址',
                  ),
                ),
                const SizedBox(height: 8),
                TextField(
                  controller: apiPathController,
                  decoration: const InputDecoration(
                    labelText: 'API Path',
                    hintText: '例如: /v1/chat/completions',
                  ),
                ),
                const SizedBox(height: 8),
                TextField(
                  controller: modelController,
                  decoration: const InputDecoration(
                    labelText: '模型标识符',
                    hintText: '请输入模型标识符',
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: variantController,
                        decoration: const InputDecoration(
                          labelText: '模型变体',
                          hintText: '添加可选的模型变体标识符',
                        ),
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.add_circle),
                      onPressed: () {
                        if (variantController.text.isNotEmpty &&
                            !modelVariants.contains(variantController.text)) {
                          setState(() {
                            modelVariants.add(variantController.text);
                            variantController.clear();
                          });
                        }
                      },
                    ),
                  ],
                ),
                if (modelVariants.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  const Text('已添加的模型变体:'),
                  SizedBox(
                    height: modelVariants.length > 3 ? 120 : null,
                    child: ListView.builder(
                      shrinkWrap: true,
                      itemCount: modelVariants.length,
                      itemBuilder: (context, index) {
                        return ListTile(
                          dense: true,
                          contentPadding: EdgeInsets.zero,
                          title: Text(modelVariants[index]),
                          trailing: IconButton(
                            icon: const Icon(Icons.delete, size: 20),
                            onPressed: () {
                              setState(() {
                                modelVariants.removeAt(index);
                              });
                            },
                          ),
                        );
                      },
                    ),
                  ),
                ],
                const SizedBox(height: 8),
                TextField(
                  controller: appIdController,
                  decoration: const InputDecoration(
                    labelText: 'App ID',
                    hintText: '部分模型需要App ID',
                  ),
                ),
                const SizedBox(height: 8),
                ThemedDropdownButtonFormField<String>(
                  value: selectedApiFormat,
                  decoration: const InputDecoration(
                    labelText: 'API 格式',
                  ),
                  items: const [
                    DropdownMenuItem(
                      value: 'OpenAI API兼容',
                      child: Text('OpenAI API兼容'),
                    ),
                    DropdownMenuItem(
                      value: 'Google API',
                      child: Text('Google API'),
                    ),
                    DropdownMenuItem(
                      value: '阿里百炼',
                      child: Text('阿里百炼'),
                    ),
                  ],
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        selectedApiFormat = value;
                      });
                    }
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                if (nameController.text.isEmpty) {
                  Get.snackbar(
                    '提示',
                    '请输入模型名称',
                    backgroundColor: Colors.red.withOpacity(0.1),
                    duration: const Duration(seconds: 2),
                  );
                  return;
                }

                if (controller.models
                    .any((m) => m.name == nameController.text)) {
                  Get.snackbar(
                    '提示',
                    '已存在同名模型，请使用其他名称',
                    backgroundColor: Colors.red.withOpacity(0.1),
                    duration: const Duration(seconds: 2),
                  );
                  return;
                }

                controller.addCustomModel(ModelConfig(
                  name: nameController.text,
                  apiKey: apiKeyController.text,
                  apiUrl: apiUrlController.text,
                  apiPath: apiPathController.text,
                  model: modelController.text,
                  modelVariants: modelVariants,
                  appId: appIdController.text,
                  apiFormat: selectedApiFormat,
                  isCustom: true,
                ));

                Navigator.of(context).pop();
                Get.snackbar(
                  '成功',
                  '模型添加成功',
                  backgroundColor: Colors.green.withOpacity(0.1),
                  duration: const Duration(seconds: 2),
                );
              },
              child: const Text('确定'),
            ),
          ],
        );
      }),
    );
  }

  Widget _buildModelDetailCard(
      BuildContext context, ApiConfigController controller, int index) {
    final model = controller.models[index];
    final isCurrentSelected = model.name == controller.selectedModelId.value;

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8),
      color: isCurrentSelected ? Colors.blue.shade50 : null,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  model.name,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: isCurrentSelected ? FontWeight.bold : null,
                        color: isCurrentSelected
                            ? Theme.of(context).primaryColor
                            : null,
                      ),
                ),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 选择此模型按钮
                    if (!isCurrentSelected)
                      IconButton(
                        icon: const Icon(Icons.check_circle_outline),
                        tooltip: '使用此模型',
                        onPressed: () {
                          controller.updateSelectedModel(model.name);
                          Get.snackbar(
                            '已切换模型',
                            '当前使用模型: ${model.name}',
                            backgroundColor: Colors.green.withOpacity(0.1),
                            duration: const Duration(seconds: 2),
                          );
                        },
                      ),
                    if (model.isCustom)
                      IconButton(
                        icon: const Icon(Icons.delete, color: Colors.red),
                        tooltip: '删除此模型',
                        onPressed: () {
                          showDialog(
                            context: context,
                            builder: (context) => AlertDialog(
                              title: const Text('确认删除'),
                              content:
                                  Text('确定要删除模型 "${model.name}" 吗？此操作不可撤销。'),
                              actions: [
                                TextButton(
                                  onPressed: () => Navigator.of(context).pop(),
                                  child: const Text('取消'),
                                ),
                                TextButton(
                                  onPressed: () {
                                    controller.removeCustomModel(model.name);
                                    Navigator.of(context).pop();
                                  },
                                  child: const Text('删除'),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 8),
            // 当前模型标识符
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('当前模型标识符:',
                          style: TextStyle(fontWeight: FontWeight.bold)),
                      const SizedBox(height: 4),
                      Text(model.model),
                    ],
                  ),
                ),
                // 编辑按钮
                IconButton(
                  icon: const Icon(Icons.edit),
                  tooltip: '编辑模型标识符',
                  onPressed: () {
                    _showEditModelIdentifierDialog(context, controller, model);
                  },
                ),
              ],
            ),

            // 模型变体列表
            if (model.modelVariants.isNotEmpty) ...[
              const SizedBox(height: 12),
              const Text('可用模型变体:',
                  style: TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: 4),
              Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: model.modelVariants.length,
                  itemBuilder: (context, variantIndex) {
                    final variant = model.modelVariants[variantIndex];
                    final isCurrentModel = variant == model.model;

                    return ListTile(
                      dense: true,
                      title: Text(
                        variant,
                        style: TextStyle(
                          fontWeight: isCurrentModel
                              ? FontWeight.bold
                              : FontWeight.normal,
                          color: isCurrentModel
                              ? Theme.of(context).primaryColor
                              : null,
                        ),
                      ),
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // 使用此变体按钮
                          if (!isCurrentModel)
                            IconButton(
                              icon: const Icon(Icons.check_circle_outline,
                                  size: 20),
                              onPressed: () {
                                controller.updateModelIdentifier(
                                    model.name, variant);
                              },
                              tooltip: '使用此模型标识符',
                            ),
                          // 删除变体按钮
                          IconButton(
                            icon: const Icon(Icons.delete,
                                size: 20, color: Colors.red),
                            onPressed: () {
                              showDialog(
                                context: context,
                                builder: (context) => AlertDialog(
                                  title: const Text('确认删除'),
                                  content: Text('确定要删除模型变体 "$variant" 吗？'),
                                  actions: [
                                    TextButton(
                                      onPressed: () =>
                                          Navigator.of(context).pop(),
                                      child: const Text('取消'),
                                    ),
                                    TextButton(
                                      onPressed: () {
                                        controller.removeModelVariant(
                                            model.name, variant);
                                        Navigator.of(context).pop();
                                      },
                                      child: const Text('删除'),
                                    ),
                                  ],
                                ),
                              );
                            },
                            tooltip: '删除此变体',
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ],

            // 添加模型变体按钮
            const SizedBox(height: 8),
            OutlinedButton.icon(
              icon: const Icon(Icons.add),
              label: const Text('添加模型变体'),
              onPressed: () {
                _showAddModelVariantDialog(context, controller, model);
              },
            ),

            const SizedBox(height: 12),
            const Divider(),

            // 模型API配置信息
            Text('API URL: ${model.apiUrl}'),
            const SizedBox(height: 4),
            Text('API 路径: ${model.apiPath}'),
            const SizedBox(height: 4),
            Text('API 格式: ${model.apiFormat}'),
            if (model.appId.isNotEmpty) ...[
              const SizedBox(height: 4),
              Text('App ID: ${model.appId}'),
            ],
            // 显示阿里云通义千问特有的深度思考模式状态
            if (model.name.contains('阿里云') ||
                model.name.contains('通义') ||
                model.apiUrl.contains('dashscope.aliyuncs.com')) ...[
              const SizedBox(height: 4),
              Text('深度思考模式: ${model.enableThinking ? '已启用' : '未启用'}'),
            ],
          ],
        ),
      ),
    );
  }

  // 显示添加模型变体对话框
  void _showAddModelVariantDialog(
      BuildContext context, ApiConfigController controller, ModelConfig model) {
    final textController = TextEditingController();
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('添加模型变体'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: textController,
              decoration: const InputDecoration(
                labelText: '模型变体标识符',
                hintText: '请输入新的模型标识符',
              ),
              autofocus: true,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              if (textController.text.isEmpty) {
                Get.snackbar(
                  '提示',
                  '请输入模型变体标识符',
                  backgroundColor: Colors.red.withOpacity(0.1),
                  duration: const Duration(seconds: 2),
                );
                return;
              }

              if (model.modelVariants.contains(textController.text) ||
                  model.model == textController.text) {
                Get.snackbar(
                  '提示',
                  '该模型变体已存在',
                  backgroundColor: Colors.red.withOpacity(0.1),
                  duration: const Duration(seconds: 2),
                );
                return;
              }

              controller.addModelVariant(model.name, textController.text);
              Navigator.of(context).pop();
              Get.snackbar(
                '成功',
                '模型变体添加成功',
                backgroundColor: Colors.green.withOpacity(0.1),
                duration: const Duration(seconds: 2),
              );
            },
            child: const Text('添加'),
          ),
        ],
      ),
    );
  }

  // 显示编辑模型标识符对话框
  void _showEditModelIdentifierDialog(
      BuildContext context, ApiConfigController controller, ModelConfig model) {
    final textController = TextEditingController(text: model.model);
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('编辑模型标识符'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: textController,
              decoration: const InputDecoration(
                labelText: '模型标识符',
                hintText: '请输入模型标识符',
              ),
              autofocus: true,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              if (textController.text.isEmpty) {
                Get.snackbar(
                  '提示',
                  '请输入模型标识符',
                  backgroundColor: Colors.red.withOpacity(0.1),
                  duration: const Duration(seconds: 2),
                );
                return;
              }

              controller.updateModelIdentifier(model.name, textController.text);
              Navigator.of(context).pop();
              Get.snackbar(
                '成功',
                '模型标识符已更新',
                backgroundColor: Colors.green.withOpacity(0.1),
                duration: const Duration(seconds: 2),
              );
            },
            child: const Text('保存'),
          ),
        ],
      ),
    );
  }

  // 当前模型变体使用情况
  Widget _buildModelVariantUsageInfo(
      BuildContext context, ApiConfigController controller) {
    final List<Widget> items = [];

    // 获取所有有变体的模型
    final modelsWithVariants = controller.models
        .where((model) => model.modelVariants.isNotEmpty)
        .toList();

    if (modelsWithVariants.isEmpty) {
      return const Padding(
        padding: EdgeInsets.all(8.0),
        child: Text('没有找到模型变体。在模型列表中添加变体以启用此功能。'),
      );
    }

    for (final model in modelsWithVariants) {
      items.add(
        ExpansionTile(
          title: Text(model.name),
          subtitle: Text('${model.modelVariants.length}个可用变体'),
          children: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('当前使用:',
                      style: TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 4),
                  Text('默认模型: ${model.model}'),

                  // 当前大纲模型变体使用情况
                  // if (controller.isDualModelMode.value) ...[
                  //   const SizedBox(height: 8),
                  //   Text('大纲模型: ${controller.outlineModelId.value.isEmpty ? model.name : controller.outlineModelId.value}'),
                  //   if (controller.outlineModelVariant.value.isNotEmpty)
                  //     Text('大纲变体: ${controller.outlineModelVariant.value}'),
                  // ],

                  // 当前章节模型变体使用情况
                  // if (controller.isDualModelMode.value) ...[
                  //   const SizedBox(height: 8),
                  //   Text('章节模型: ${controller.chapterModelId.value.isEmpty ? model.name : controller.chapterModelId.value}'),
                  //   if (controller.chapterModelVariant.value.isNotEmpty)
                  //     Text('章节变体: ${controller.chapterModelVariant.value}'),
                  // ],
                ],
              ),
            ),
          ],
        ),
      );

      items.add(const Divider());
    }

    return Column(children: items);
  }
}
