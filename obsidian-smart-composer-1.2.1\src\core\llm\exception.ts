export class LLMAPI<PERSON>eyNotSetException extends Error {
  constructor(
    message: string,
    public rawError?: Error,
  ) {
    super(message)
    this.name = 'LLMAPIKeyNotSetException'
  }
}

export class LLMAPIKeyInvalidException extends Error {
  constructor(
    message: string,
    public rawError?: Error,
  ) {
    super(message)
    this.name = 'LLMAPIKeyInvalidException'
  }
}

export class LLMBaseUrlNotSetException extends Error {
  constructor(
    message: string,
    public rawError?: Error,
  ) {
    super(message)
    this.name = 'LLMBaseUrlNotSetException'
  }
}

export class LLMRateLimitExceededException extends Error {
  constructor(
    message: string,
    public rawError?: Error,
  ) {
    super(message)
    this.name = 'LLMRateLimitExceededException'
  }
}
